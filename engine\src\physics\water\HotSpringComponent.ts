/**
 * 温泉组件
 * 用于表示温泉及其特殊物理属性和效果
 */
import * as THREE from 'three';
import { WaterBodyComponent, WaterBodyType } from './WaterBodyComponent';
import type { Entity } from '../../core/Entity';
import { Debug } from '../../utils/Debug';
import { AudioSource } from '../../audio/AudioSource';

/**
 * 温泉配置
 */
export interface HotSpringConfig {
  /** 温泉类型 */
  hotSpringType?: HotSpringType;
  /** 温泉宽度 */
  width?: number;
  /** 温泉高度 */
  height?: number;
  /** 温泉深度 */
  depth?: number;
  /** 温泉位置 */
  position?: THREE.Vector3;
  /** 温泉旋转 */
  rotation?: THREE.Euler;
  /** 温泉颜色 */
  color?: THREE.Color;
  /** 温泉不透明度 */
  opacity?: number;
  /** 温泉温度 */
  temperature?: number;
  /** 温泉波动强度 */
  waveAmplitude?: number;
  /** 温泉波动频率 */
  waveFrequency?: number;
  /** 温泉波动速度 */
  waveSpeed?: number;
  /** 是否启用气泡效果 */
  enableBubbleEffect?: boolean;
  /** 气泡效果强度 */
  bubbleEffectStrength?: number;
  /** 气泡大小范围 */
  bubbleSizeRange?: [number, number];
  /** 气泡速度范围 */
  bubbleSpeedRange?: [number, number];
  /** 气泡密度 */
  bubbleDensity?: number;
  /** 气泡分布范围 */
  bubbleDistributionRadius?: number;
  /** 是否启用气泡爆裂效果 */
  enableBubbleBurstEffect?: boolean;
  /** 气泡爆裂效果强度 */
  bubbleBurstEffectStrength?: number;
  /** 是否启用水蒸气效果 */
  enableSteamEffect?: boolean;
  /** 水蒸气效果强度 */
  steamEffectStrength?: number;
  /** 水蒸气颜色 */
  steamColor?: THREE.Color;
  /** 水蒸气密度 */
  steamDensity?: number;
  /** 水蒸气大小范围 */
  steamSizeRange?: [number, number];
  /** 水蒸气速度范围 */
  steamSpeedRange?: [number, number];
  /** 水蒸气上升高度 */
  steamRiseHeight?: number;
  /** 是否启用声音效果 */
  enableSoundEffect?: boolean;
  /** 声音效果音量 */
  soundEffectVolume?: number;
  /** 是否启用热扩散效果 */
  enableHeatDiffusion?: boolean;
  /** 热扩散范围 */
  heatDiffusionRange?: number;
  /** 是否启用矿物质效果 */
  enableMineralEffect?: boolean;
  /** 矿物质颜色 */
  mineralColor?: THREE.Color;
}

/**
 * 温泉类型
 */
export enum HotSpringType {
  /** 标准温泉 */
  STANDARD = 'standard',
  /** 高温温泉 */
  HIGH_TEMPERATURE = 'high_temperature',
  /** 低温温泉 */
  LOW_TEMPERATURE = 'low_temperature',
  /** 硫磺温泉 */
  SULFUR = 'sulfur',
  /** 矿物质温泉 */
  MINERAL = 'mineral',
  /** 地下温泉 */
  UNDERGROUND = 'underground'
}

/**
 * 温泉组件
 */
export class HotSpringComponent extends WaterBodyComponent {
  /** 温泉类型 */
  private hotSpringType: HotSpringType = HotSpringType.STANDARD;
  /** 温泉温度 */
  private hotSpringTemperature: number = 60.0; // 摄氏度
  /** 温泉波动强度 */
  private waveAmplitude: number = 0.1;
  /** 温泉波动频率 */
  private waveFrequency: number = 2.0;
  /** 温泉波动速度 */
  private waveSpeed: number = 0.5;
  /** 是否启用气泡效果 */
  private enableBubbleEffect: boolean = true;
  /** 气泡效果强度 */
  private bubbleEffectStrength: number = 1.0;
  /** 气泡大小范围 */
  private bubbleSizeRange: [number, number] = [0.05, 0.2];
  /** 气泡速度范围 */
  private bubbleSpeedRange: [number, number] = [0.1, 0.3];
  /** 气泡密度 */
  private bubbleDensity: number = 1.0;
  /** 气泡分布范围 */
  private bubbleDistributionRadius: number = 0.8;
  /** 是否启用气泡爆裂效果 */
  private enableBubbleBurstEffect: boolean = true;
  /** 气泡爆裂效果强度 */
  private bubbleBurstEffectStrength: number = 1.0;
  /** 是否启用水蒸气效果 */
  private enableSteamEffect: boolean = true;
  /** 水蒸气效果强度 */
  private steamEffectStrength: number = 1.0;
  /** 水蒸气颜色 */
  private steamColor: THREE.Color = new THREE.Color(0xffffff);
  /** 水蒸气密度 */
  private steamDensity: number = 1.0;
  /** 水蒸气大小范围 */
  private steamSizeRange: [number, number] = [0.5, 1.5];
  /** 水蒸气速度范围 */
  private steamSpeedRange: [number, number] = [0.05, 0.1];
  /** 水蒸气上升高度 */
  private steamRiseHeight: number = 2.0;
  /** 是否启用声音效果 */
  private enableSoundEffect: boolean = true;
  /** 声音效果音量 */
  private soundEffectVolume: number = 1.0;
  /** 是否启用热扩散效果 */
  private enableHeatDiffusion: boolean = true;
  /** 热扩散范围 */
  private heatDiffusionRange: number = 5.0;
  /** 是否启用矿物质效果 */
  private enableMineralEffect: boolean = true;
  /** 矿物质颜色 */
  private mineralColor: THREE.Color = new THREE.Color(0xc0a080);
  /** 音频源 */
  private audioSource: AudioSource | null = null;
  /** 矿物质边缘 */
  private mineralEdge: THREE.Mesh | null = null;
  /** 热扩散区域 */
  private heatDiffusionArea: THREE.Mesh | null = null;

  /**
   * 创建温泉组件
   * @param entity 实体
   * @param config 温泉配置
   */
  constructor(entity: Entity, config: HotSpringConfig = {}) {
    // 确保水体类型设置为温泉
    const hotSpringConfig = {
      ...config,
      type: WaterBodyType.HOT_SPRING
    };

    super(entity, hotSpringConfig);

    // 应用配置
    this.applyConfig(config);

    // 初始化温泉特有属性
    this.initialize();
  }

  /**
   * 应用配置
   * @param config 温泉配置
   */
  private applyConfig(config: HotSpringConfig): void {
    // 基本配置通过父类构造函数已经处理，这里只处理温泉特有配置

    // 应用温泉特有配置
    if (config.hotSpringType !== undefined) this.hotSpringType = config.hotSpringType;
    if (config.temperature !== undefined) this.hotSpringTemperature = config.temperature;
    if (config.waveAmplitude !== undefined) this.waveAmplitude = config.waveAmplitude;
    if (config.waveFrequency !== undefined) this.waveFrequency = config.waveFrequency;
    if (config.waveSpeed !== undefined) this.waveSpeed = config.waveSpeed;

    // 应用气泡效果配置
    if (config.enableBubbleEffect !== undefined) this.enableBubbleEffect = config.enableBubbleEffect;
    if (config.bubbleEffectStrength !== undefined) this.bubbleEffectStrength = config.bubbleEffectStrength;
    if (config.bubbleSizeRange !== undefined) this.bubbleSizeRange = config.bubbleSizeRange;
    if (config.bubbleSpeedRange !== undefined) this.bubbleSpeedRange = config.bubbleSpeedRange;
    if (config.bubbleDensity !== undefined) this.bubbleDensity = config.bubbleDensity;
    if (config.bubbleDistributionRadius !== undefined) this.bubbleDistributionRadius = config.bubbleDistributionRadius;

    // 应用气泡爆裂效果配置
    if (config.enableBubbleBurstEffect !== undefined) this.enableBubbleBurstEffect = config.enableBubbleBurstEffect;
    if (config.bubbleBurstEffectStrength !== undefined) this.bubbleBurstEffectStrength = config.bubbleBurstEffectStrength;

    // 应用水蒸气效果配置
    if (config.enableSteamEffect !== undefined) this.enableSteamEffect = config.enableSteamEffect;
    if (config.steamEffectStrength !== undefined) this.steamEffectStrength = config.steamEffectStrength;
    if (config.steamColor) this.steamColor = config.steamColor;
    if (config.steamDensity !== undefined) this.steamDensity = config.steamDensity;
    if (config.steamSizeRange !== undefined) this.steamSizeRange = config.steamSizeRange;
    if (config.steamSpeedRange !== undefined) this.steamSpeedRange = config.steamSpeedRange;
    if (config.steamRiseHeight !== undefined) this.steamRiseHeight = config.steamRiseHeight;

    // 应用其他效果配置
    if (config.enableSoundEffect !== undefined) this.enableSoundEffect = config.enableSoundEffect;
    if (config.soundEffectVolume !== undefined) this.soundEffectVolume = config.soundEffectVolume;
    if (config.enableHeatDiffusion !== undefined) this.enableHeatDiffusion = config.enableHeatDiffusion;
    if (config.heatDiffusionRange !== undefined) this.heatDiffusionRange = config.heatDiffusionRange;
    if (config.enableMineralEffect !== undefined) this.enableMineralEffect = config.enableMineralEffect;
    if (config.mineralColor) this.mineralColor = config.mineralColor;
  }

  /**
   * 初始化温泉组件
   */
  public initialize(): void {
    // 调用父类初始化
    super.initialize();

    // 设置波动参数（如果父类有此方法）
    if (typeof (this as any).setWaveParams === 'function') {
      (this as any).setWaveParams({
        amplitude: this.waveAmplitude,
        frequency: this.waveFrequency,
        speed: this.waveSpeed,
        direction: new THREE.Vector2(1, 1)
      });
    }

    // 初始化音频
    this.initializeAudio();

    // 初始化粒子系统
    this.initializeParticleSystems();

    // 创建矿物质边缘
    this.createMineralEdge();

    // 创建热扩散区域
    this.createHeatDiffusionArea();

    Debug.log('HotSpringComponent', '温泉组件初始化完成');
  }

  /**
   * 初始化音频
   */
  private initializeAudio(): void {
    // 音频系统暂时禁用，等待系统实现
    Debug.log('HotSpringComponent', '音频系统暂时禁用');
  }

  /**
   * 初始化粒子系统
   */
  private initializeParticleSystems(): void {
    // 粒子系统暂时禁用，等待系统实现
    Debug.log('HotSpringComponent', '粒子系统暂时禁用');
  }

  // 移除未使用的方法

  /**
   * 创建矿物质边缘
   */
  private createMineralEdge(): void {
    // 矿物质边缘功能暂时禁用，等待实现
    Debug.log('HotSpringComponent', '矿物质边缘功能暂时禁用');
  }

  /**
   * 创建热扩散区域
   */
  private createHeatDiffusionArea(): void {
    // 热扩散区域功能暂时禁用，等待实现
    Debug.log('HotSpringComponent', '热扩散区域功能暂时禁用');
  }

  /**
   * 更新温泉组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    // 调用父类更新
    super.update(deltaTime);

    // 所有更新功能暂时禁用，等待系统实现
    Debug.log('HotSpringComponent', '更新功能暂时禁用');
  }

  // 移除未使用的方法

  // 移除所有未使用的方法

  /**
   * 销毁温泉组件
   */
  public destroy(): void {
    // 清理资源
    if (this.audioSource) {
      this.audioSource = null;
    }

    if (this.mineralEdge) {
      (this.mineralEdge.geometry as any).dispose();
      (this.mineralEdge.material as THREE.Material).dispose();
      this.mineralEdge = null;
    }

    if (this.heatDiffusionArea) {
      (this.heatDiffusionArea.geometry as any).dispose();
      (this.heatDiffusionArea.material as THREE.Material).dispose();
      this.heatDiffusionArea = null;
    }

    Debug.log('HotSpringComponent', '温泉组件已销毁');
  }

  /**
   * 获取温泉温度
   * @returns 温泉温度（摄氏度）
   */
  public getTemperature(): number {
    return this.hotSpringTemperature;
  }

  /**
   * 设置温泉温度
   * @param temperature 温泉温度（摄氏度）
   */
  public setTemperature(temperature: number): void {
    this.hotSpringTemperature = temperature;
  }

  /**
   * 获取波动强度
   * @returns 波动强度
   */
  public getWaveAmplitude(): number {
    return this.waveAmplitude;
  }

  /**
   * 设置波动强度
   * @param amplitude 波动强度
   */
  public setWaveAmplitude(amplitude: number): void {
    this.waveAmplitude = amplitude;
  }

  /**
   * 获取波动频率
   * @returns 波动频率
   */
  public getWaveFrequency(): number {
    return this.waveFrequency;
  }

  /**
   * 设置波动频率
   * @param frequency 波动频率
   */
  public setWaveFrequency(frequency: number): void {
    this.waveFrequency = frequency;
  }

  /**
   * 获取波动速度
   * @returns 波动速度
   */
  public getWaveSpeed(): number {
    return this.waveSpeed;
  }

  /**
   * 设置波动速度
   * @param speed 波动速度
   */
  public setWaveSpeed(speed: number): void {
    this.waveSpeed = speed;
  }

  /**
   * 获取温泉类型
   * @returns 温泉类型
   */
  public getHotSpringType(): HotSpringType {
    return this.hotSpringType;
  }

  /**
   * 设置温泉类型
   * @param type 温泉类型
   */
  public setHotSpringType(type: HotSpringType): void {
    this.hotSpringType = type;
  }
}
